# WPF桌面应用界面设计开发规则文档

## 1. 项目概述与技术规格

### 1.1 技术规格要求
- **目标平台**: WPF桌面应用程序 (.NET Framework 4.8+ / .NET 6+)
- **最低分辨率**: 1280×800像素
- **响应式支持**: 自适应1920×1080、2560×1440等高分辨率
- **实现技术**: XAML + WPF技术栈
- **设计工具**: HTML原型 + 详细标注图

### 1.2 设计定位
- **核心设计语言**: Material Design 3
- **视觉风格**: 科技感、现代化、工业风格、美观实用
- **用户体验**: 直观、高效、一致性

## 2. 设计系统基础规范

### 2.1 色彩体系 (Material Design 3 适配)

#### 主色调系统
```
主色调 (Primary):
- Primary-50: #E3F2FD    (最浅背景)
- Primary-100: #BBDEFB   (浅色背景)
- Primary-500: #2196F3   (主要按钮、链接)
- Primary-700: #1976D2   (深色主色)
- Primary-900: #0D47A1   (最深主色)

辅助色 (Secondary):
- Secondary-50: #F3E5F5
- Secondary-100: #E1BEE7
- Secondary-500: #9C27B0
- Secondary-700: #7B1FA2
- Secondary-900: #4A148C

中性色 (Neutral):
- Surface: #FAFAFA       (卡片背景)
- Background: #FFFFFF    (主背景)
- On-Surface: #1C1B1F    (主要文字)
- On-Background: #1C1B1F (背景上文字)
- Outline: #79747E       (边框线)
- Outline-Variant: #CAC4D0 (次要边框)

语义色系统:
- Success: #4CAF50       (成功状态)
- Warning: #FF9800       (警告状态)
- Error: #F44336         (错误状态)
- Info: #2196F3          (信息提示)
```

#### 科技工业风格色彩增强
```
科技蓝色系:
- Tech-Blue-Primary: #00BCD4    (科技主色)
- Tech-Blue-Dark: #0097A7       (深科技蓝)
- Tech-Blue-Light: #B2EBF2      (浅科技蓝)

工业灰色系:
- Industrial-Dark: #263238      (深工业灰)
- Industrial-Medium: #455A64    (中工业灰)
- Industrial-Light: #ECEFF1     (浅工业灰)

强调色:
- Accent-Orange: #FF5722        (橙色强调)
- Accent-Green: #00E676         (绿色强调)
```

### 2.2 字体层级系统

#### 字体族选择
```
主字体: Microsoft YaHei UI (中文) / Segoe UI (英文)
代码字体: Consolas / Courier New
数字字体: Segoe UI / Arial
```

#### 字体层级规范
```
Display Large:    32px, Bold, 行高40px    (页面主标题)
Display Medium:   28px, Bold, 行高36px    (区块标题)
Display Small:    24px, Bold, 行高32px    (卡片标题)

Headline Large:   22px, Medium, 行高28px  (重要标题)
Headline Medium:  18px, Medium, 行高24px  (次要标题)
Headline Small:   16px, Medium, 行高20px  (小标题)

Body Large:       16px, Regular, 行高24px (主要正文)
Body Medium:      14px, Regular, 行高20px (次要正文)
Body Small:       12px, Regular, 行高16px (说明文字)

Label Large:      14px, Medium, 行高20px  (按钮文字)
Label Medium:     12px, Medium, 行高16px  (标签文字)
Label Small:      10px, Medium, 行高14px  (小标签)
```

### 2.3 间距系统 (8px网格)

#### 基础间距单位
```
XS: 4px    (最小间距)
S:  8px    (小间距)
M:  16px   (中等间距)
L:  24px   (大间距)
XL: 32px   (超大间距)
XXL: 48px  (页面级间距)
```

#### 组件内部间距
```
按钮内边距: 12px 24px
输入框内边距: 12px 16px
卡片内边距: 16px 20px
对话框内边距: 24px 24px
```

#### 组件外部间距
```
组件间垂直间距: 16px
区块间垂直间距: 24px
页面边距: 24px
```

### 2.4 圆角和阴影系统

#### 圆角规范
```
无圆角: 0px        (分割线、边框)
小圆角: 4px        (按钮、输入框)
中圆角: 8px        (卡片、面板)
大圆角: 12px       (对话框、抽屉)
圆形: 50%          (头像、图标按钮)
```

#### 阴影系统 (Material Design 3)
```
Elevation 1: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)
Elevation 2: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)
Elevation 3: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23)
Elevation 4: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22)
```

## 3. 界面布局原则

### 3.1 响应式设计策略

#### 断点系统
```
Small:  1280px - 1439px  (最小支持)
Medium: 1440px - 1919px  (常见分辨率)
Large:  1920px - 2559px  (高清屏幕)
XLarge: 2560px+          (4K及以上)
```

#### 布局适配原则
1. **流式布局**: 使用Grid和StackPanel实现自适应
2. **比例缩放**: 关键元素使用相对尺寸
3. **内容优先**: 确保核心功能在最小分辨率下可用
4. **渐进增强**: 高分辨率下提供更丰富的视觉效果

### 3.2 网格系统

#### 12列网格系统
```
容器最大宽度: 1200px (居中对齐)
列宽计算: (容器宽度 - 间距) / 12
间距: 24px (列间距)
边距: 24px (容器边距)
```

#### 常用布局模式
```
侧边栏布局: 3列 + 9列
双栏布局: 6列 + 6列
三栏布局: 4列 + 4列 + 4列
主内容布局: 2列边距 + 8列内容 + 2列边距
```

## 4. Material Design 3 在WPF中的适配指南

### 4.1 核心组件适配

#### 按钮系统
```
Filled Button (主要按钮):
- 背景: Primary-500
- 文字: White
- 圆角: 4px
- 高度: 40px
- 内边距: 12px 24px
- WPF实现: 自定义Button Template

Outlined Button (次要按钮):
- 背景: Transparent
- 边框: 1px Primary-500
- 文字: Primary-500
- WPF实现: Border + Button

Text Button (文本按钮):
- 背景: Transparent
- 文字: Primary-500
- 无边框
- WPF实现: Button with custom style
```

#### 输入框系统
```
Filled TextField:
- 背景: Surface
- 边框: 底部2px Primary-500
- 标签: 浮动标签动画
- WPF实现: TextBox + Label + Storyboard

Outlined TextField:
- 背景: Transparent
- 边框: 1px Outline
- 聚焦边框: 2px Primary-500
- WPF实现: Border + TextBox + 触发器
```

### 4.2 动画和过渡效果

#### 标准动画时长
```
快速动画: 150ms   (按钮点击、悬停)
标准动画: 300ms   (页面切换、抽屉)
慢速动画: 500ms   (复杂布局变化)
```

#### 缓动函数
```
标准缓动: cubic-bezier(0.4, 0.0, 0.2, 1)
进入缓动: cubic-bezier(0.0, 0.0, 0.2, 1)
退出缓动: cubic-bezier(0.4, 0.0, 1, 1)
```

#### WPF动画实现
```
透明度动画: DoubleAnimation on Opacity
位移动画: ThicknessAnimation on Margin
缩放动画: ScaleTransform + DoubleAnimation
旋转动画: RotateTransform + DoubleAnimation
```

## 5. 科技工业风格实现方法

### 5.1 视觉元素特征
```
线条风格: 细线条、几何图形、网格背景
图标风格: 线性图标、几何图标
质感效果: 金属质感、玻璃效果、发光效果
数据可视化: 图表、进度条、仪表盘
```

### 5.2 特殊效果实现
```
发光效果: DropShadowEffect + BlurEffect
金属质感: LinearGradientBrush + 多层渐变
玻璃效果: 半透明背景 + 模糊效果
网格背景: Canvas + 重复几何图形
```

## 6. 设计一致性保证机制

### 6.1 组件库标准化
```
基础组件清单:
- Button (5种变体)
- TextBox (3种变体)  
- ComboBox (2种变体)
- CheckBox / RadioButton
- ProgressBar / Slider
- Card / Panel
- Dialog / MessageBox
- Navigation / Menu
- DataGrid / ListView
- TabControl / Expander
```

### 6.2 状态规范
```
交互状态:
- Normal: 默认状态
- Hover: 鼠标悬停 (透明度0.8或颜色变化)
- Pressed: 点击状态 (缩放0.95或颜色加深)
- Focused: 键盘焦点 (外边框高亮)
- Disabled: 禁用状态 (透明度0.5)

数据状态:
- Loading: 加载中 (进度指示器)
- Empty: 空状态 (占位图标和文字)
- Error: 错误状态 (错误图标和提示)
- Success: 成功状态 (成功图标和提示)
```

## 7. 控制指令系统

### 7.1 设计指令格式
```
/设计 <界面名称> [选项]
/原型 <界面名称> [选项]
/标注 <界面名称> [选项]
/组件 <组件名称> [选项]
/主题 <主题名称> [选项]
```

### 7.2 常用指令示例
```
/设计 登录界面 --风格=科技
/设计 主界面 --布局=侧边栏 --主题=深色
/原型 用户管理 --交互=完整
/标注 设置页面 --详细=全部
/组件 数据表格 --功能=分页+筛选
/主题 深色模式 --对比度=高
```

### 7.3 选项参数说明
```
风格选项: 科技、商务、简约、工业
布局选项: 单栏、双栏、侧边栏、网格
主题选项: 浅色、深色、自动
交互选项: 基础、完整、演示
详细选项: 基础、详细、全部
功能选项: 根据组件类型定义
对比度选项: 标准、高、最高
```

## 8. HTML原型制作标准和规范

### 8.1 原型制作要求

#### 技术栈选择
```
基础技术: HTML5 + CSS3 + JavaScript
CSS框架: 自定义CSS (基于Material Design 3)
交互库: 原生JavaScript或轻量级库
响应式: CSS Grid + Flexbox
动画: CSS Transitions + Animations
```

#### 原型保真度标准
```
高保真要求:
- 像素级精确的视觉还原
- 真实的颜色、字体、间距
- 完整的交互状态展示
- 响应式布局演示
- 基础动画效果

交互功能要求:
- 按钮点击反馈
- 表单输入验证
- 页面/标签切换
- 模态框显示/隐藏
- 下拉菜单展开/收起
- 数据列表的基础操作
```

#### 文件结构规范
```
prototype/
├── index.html              (主页面)
├── css/
│   ├── reset.css          (样式重置)
│   ├── variables.css      (设计变量)
│   ├── components.css     (组件样式)
│   └── pages.css          (页面样式)
├── js/
│   ├── main.js           (主要交互逻辑)
│   └── components.js     (组件交互)
├── assets/
│   ├── icons/            (图标文件)
│   └── images/           (图片资源)
└── pages/                (其他页面)
```

### 8.2 CSS变量系统
```css
:root {
  /* 颜色系统 */
  --primary-50: #E3F2FD;
  --primary-500: #2196F3;
  --primary-700: #1976D2;

  /* 字体系统 */
  --font-family-primary: 'Microsoft YaHei UI', 'Segoe UI', sans-serif;
  --font-size-display-large: 32px;
  --font-size-body-large: 16px;

  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-s: 8px;
  --spacing-m: 16px;
  --spacing-l: 24px;

  /* 圆角系统 */
  --border-radius-small: 4px;
  --border-radius-medium: 8px;

  /* 阴影系统 */
  --elevation-1: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  --elevation-2: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
}
```

### 8.3 组件样式模板
```css
/* 按钮组件 */
.btn-filled {
  background-color: var(--primary-500);
  color: white;
  border: none;
  border-radius: var(--border-radius-small);
  padding: 12px 24px;
  font-size: var(--font-size-body-large);
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-filled:hover {
  background-color: var(--primary-700);
  box-shadow: var(--elevation-2);
}

/* 输入框组件 */
.textfield-outlined {
  position: relative;
  margin: var(--spacing-m) 0;
}

.textfield-outlined input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--outline);
  border-radius: var(--border-radius-small);
  font-size: var(--font-size-body-large);
  transition: border-color 0.3s ease;
}

.textfield-outlined input:focus {
  outline: none;
  border-color: var(--primary-500);
  border-width: 2px;
}
```

## 9. 尺寸标注详细要求和格式

### 9.1 标注图制作规范

#### 标注工具推荐
```
专业工具: Figma、Adobe XD、Sketch
在线工具: Figma (推荐)
标注插件: Figma Dev Mode、Zeplin
导出格式: PNG (高分辨率) + 可编辑源文件
```

#### 标注内容要求
```
必须标注项目:
1. 所有控件的宽度和高度
2. 控件之间的水平和垂直间距
3. 容器的内边距和外边距
4. 字体大小、行高、字重
5. 颜色值 (HEX、RGB)
6. 圆角半径值
7. 边框宽度和样式
8. 阴影参数

可选标注项目:
1. 动画时长和缓动函数
2. 交互状态的变化参数
3. 响应式断点和变化规则
4. 特殊效果的实现参数
```

### 9.2 标注格式规范

#### 尺寸标注格式
```
宽度标注: W: 240px
高度标注: H: 40px
间距标注: ↔ 16px (水平) / ↕ 24px (垂直)
内边距: Padding: 12px 16px
外边距: Margin: 16px 0
```

#### 字体标注格式
```
字体标注: Microsoft YaHei UI / 16px / 400 / #1C1B1F
格式说明: 字体族 / 字号 / 字重 / 颜色
行高标注: Line Height: 24px (150%)
字间距: Letter Spacing: 0px (如有调整)
```

#### 颜色标注格式
```
颜色标注: #2196F3 / RGB(33, 150, 243) / HSL(207, 90%, 54%)
透明度: Opacity: 80% 或 Alpha: 0.8
渐变色: Linear Gradient: #2196F3 → #1976D2 (90deg)
```

### 9.3 标注图分层结构
```
图层组织:
├── 界面截图层 (最底层)
├── 尺寸标注层
├── 间距标注层
├── 字体标注层
├── 颜色标注层
└── 说明文字层 (最顶层)

标注颜色规范:
- 尺寸线: #FF5722 (橙色)
- 间距线: #4CAF50 (绿色)
- 字体标注: #9C27B0 (紫色)
- 颜色标注: #2196F3 (蓝色)
- 说明文字: #1C1B1F (深灰)
```

## 10. WPF技术实现约束条件和最佳实践

### 10.1 XAML实现约束

#### 布局容器选择
```
Grid: 复杂布局、响应式设计 (推荐)
StackPanel: 简单线性布局
WrapPanel: 自动换行布局
Canvas: 绝对定位布局 (谨慎使用)
DockPanel: 停靠布局
UniformGrid: 均匀网格布局
```

#### 样式和模板限制
```
支持的样式属性:
- Background、Foreground (纯色、渐变、图片)
- BorderBrush、BorderThickness
- Padding、Margin
- FontFamily、FontSize、FontWeight
- Opacity、Visibility

模板自定义能力:
- ControlTemplate: 完全自定义控件外观
- DataTemplate: 自定义数据显示
- ItemsPanelTemplate: 自定义容器布局
- Style: 属性设置和触发器
```

#### 动画实现方案
```
基础动画类型:
- DoubleAnimation: 数值属性动画
- ColorAnimation: 颜色属性动画
- ThicknessAnimation: 边距动画
- PointAnimation: 点位置动画

复合动画:
- Storyboard: 动画序列管理
- ParallelTimeline: 并行动画
- BeginStoryboard: 触发器中启动动画

性能优化:
- 使用RenderTransform代替布局属性
- 避免频繁的布局计算
- 合理使用动画缓存
```

### 10.2 性能优化指南

#### 渲染性能优化
```
最佳实践:
1. 使用硬件加速 (RenderOptions.ProcessRenderMode)
2. 避免复杂的透明度叠加
3. 合理使用CacheMode
4. 减少不必要的数据绑定
5. 使用虚拟化控件 (VirtualizingStackPanel)

避免的做法:
1. 过度使用Effect (DropShadowEffect等)
2. 频繁的UI线程操作
3. 大量嵌套的容器
4. 不必要的动画
```

#### 内存管理
```
资源管理:
- 合理使用StaticResource和DynamicResource
- 及时释放事件订阅
- 避免循环引用
- 使用WeakReference处理大对象

数据绑定优化:
- 使用OneWay绑定代替TwoWay (如适用)
- 实现INotifyPropertyChanged接口
- 使用ObservableCollection
- 避免绑定到复杂计算属性
```

### 10.3 兼容性考虑

#### .NET版本兼容性
```
.NET Framework 4.8:
- 完整的WPF功能支持
- 良好的第三方库兼容性
- Windows 7+ 支持

.NET 6/7/8:
- 现代化的开发体验
- 更好的性能
- 仅支持Windows 10+
- 部分第三方库可能不兼容
```

#### 系统兼容性
```
Windows版本支持:
- Windows 10: 完全支持
- Windows 11: 完全支持 + 新特性
- Windows 8.1: 基础支持 (.NET Framework)
- Windows 7: 有限支持 (.NET Framework)

DPI缩放支持:
- 设置PerMonitorV2 DPI感知
- 使用相对单位而非绝对像素
- 测试不同DPI设置下的显示效果
```

## 11. 质量检查清单和验收标准

### 11.1 设计质量检查清单

#### 视觉设计检查
```
□ 颜色使用符合设计系统规范
□ 字体大小和层级正确
□ 间距符合8px网格系统
□ 圆角和阴影使用一致
□ 图标风格统一
□ 对比度符合无障碍标准 (WCAG 2.1 AA)
□ 科技工业风格元素运用恰当
□ 整体视觉层次清晰
```

#### 布局设计检查
```
□ 响应式布局在各分辨率下正常显示
□ 网格系统使用正确
□ 内容优先级明确
□ 导航逻辑清晰
□ 信息架构合理
□ 空状态和错误状态设计完整
□ 加载状态设计合理
□ 交互反馈及时明确
```

#### 一致性检查
```
□ 同类组件样式统一
□ 交互行为一致
□ 文案风格统一
□ 图标使用规范
□ 动画效果协调
□ 状态变化逻辑一致
□ 错误处理方式统一
□ 成功反馈方式统一
```

### 11.2 技术实现检查

#### HTML原型检查
```
□ 代码结构清晰规范
□ CSS变量使用正确
□ 响应式布局实现完整
□ 交互功能正常
□ 动画效果流畅
□ 浏览器兼容性良好
□ 性能表现良好
□ 代码注释完整
```

#### WPF实现可行性检查
```
□ 所有设计元素都能在WPF中实现
□ 性能影响在可接受范围内
□ 兼容性要求满足
□ 技术实现方案明确
□ 开发工作量评估合理
□ 维护成本可控
□ 扩展性良好
□ 测试方案完整
```

### 11.3 验收标准

#### 设计交付物验收
```
必须交付:
1. 高保真HTML原型 (可交互)
2. 详细尺寸标注图 (Figma源文件 + PNG导出)
3. 设计规范文档 (颜色、字体、间距等)
4. WPF实现方案说明
5. 组件库文档

质量标准:
- 原型与最终设计99%一致
- 标注信息100%完整准确
- 技术方案100%可行
- 文档描述清晰无歧义
- 所有交互状态都有设计
```

#### 项目验收标准
```
设计一致性: 95%以上界面符合设计规范
用户体验: 通过可用性测试
技术实现: 所有功能正常运行
性能表现: 界面响应时间<300ms
兼容性: 支持目标平台和分辨率
可维护性: 代码结构清晰，文档完整
可扩展性: 支持后续功能扩展
```

## 12. 工作流程和协作规范

### 12.1 设计开发流程

#### 阶段一：需求分析和规划 (1-2天)
```
1. 需求收集和分析
2. 用户场景梳理
3. 功能模块划分
4. 技术可行性评估
5. 设计系统建立
6. 项目计划制定
```

#### 阶段二：界面设计 (3-5天)
```
1. 信息架构设计
2. 线框图绘制
3. 视觉设计
4. 交互设计
5. 响应式适配
6. 设计评审
```

#### 阶段三：原型制作 (2-3天)
```
1. HTML原型开发
2. 交互功能实现
3. 响应式测试
4. 浏览器兼容性测试
5. 性能优化
6. 原型评审
```

#### 阶段四：标注和文档 (1-2天)
```
1. 详细尺寸标注
2. 技术实现方案
3. 组件库文档
4. 开发指南
5. 测试用例
6. 最终交付
```

### 12.2 版本管理和迭代

#### 版本命名规范
```
主版本.次版本.修订版本
例如: 1.0.0, 1.1.0, 1.1.1

主版本: 重大设计变更
次版本: 新增功能或界面
修订版本: 问题修复和优化
```

#### 变更管理流程
```
1. 变更需求提出
2. 影响范围评估
3. 设计方案调整
4. 原型更新
5. 文档同步更新
6. 版本发布
```

---

## 附录

### A. 常用WPF控件样式模板

#### A.1 按钮样式模板
```xml
<Style x:Key="MaterialFilledButton" TargetType="Button">
    <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
    <Setter Property="Foreground" Value="White"/>
    <Setter Property="BorderThickness" Value="0"/>
    <Setter Property="Padding" Value="24,12"/>
    <Setter Property="FontSize" Value="14"/>
    <Setter Property="FontWeight" Value="Medium"/>
    <Setter Property="Template">
        <Setter.Value>
            <ControlTemplate TargetType="Button">
                <Border Background="{TemplateBinding Background}"
                        CornerRadius="4"
                        Padding="{TemplateBinding Padding}">
                    <ContentPresenter HorizontalAlignment="Center"
                                    VerticalAlignment="Center"/>
                </Border>
                <ControlTemplate.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
                    </Trigger>
                </ControlTemplate.Triggers>
            </ControlTemplate>
        </Setter.Value>
    </Setter>
</Style>
```

### B. 科技风格特效实现

#### B.1 发光效果实现
```xml
<Border.Effect>
    <DropShadowEffect Color="#00BCD4"
                      BlurRadius="10"
                      ShadowDepth="0"
                      Opacity="0.8"/>
</Border.Effect>
```

#### B.2 渐变背景实现
```xml
<Border.Background>
    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#263238" Offset="0"/>
        <GradientStop Color="#455A64" Offset="1"/>
    </LinearGradientBrush>
</Border.Background>
```

### C. 响应式布局示例

#### C.1 自适应网格布局
```xml
<Grid>
    <Grid.ColumnDefinitions>
        <ColumnDefinition Width="*" MinWidth="300"/>
        <ColumnDefinition Width="2*" MinWidth="600"/>
        <ColumnDefinition Width="*" MinWidth="300"/>
    </Grid.ColumnDefinitions>
    <!-- 内容区域 -->
</Grid>
```

---

*本文档版本: v1.0.0*
*最后更新: 2024年*
*文档状态: 正式版*

**使用说明**: 本文档为WPF桌面应用界面设计的完整指导文件，请严格按照规范执行所有设计工作。如有疑问或需要更新，请及时反馈。
